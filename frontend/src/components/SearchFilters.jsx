// src/components/SearchFilter.jsx
import React from "react";

const SearchFilter = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 relative z-10 mt-12">
      <div className="bg-white rounded-2xl shadow-2xl px-6 py-6 h-[25vh]">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4 items-end">
          <div className="flex flex-col w-full">
            <label className="text-sm text-gray-600 mb-1">Service</label>
            <select className="border border-gray-300 rounded-md py-2 px-3 w-full">
              <option>What service?</option>
            </select>
          </div>
          <div className="flex flex-col w-full">
            <label className="text-sm text-gray-600 mb-1">Location</label>
            <input
              type="text"
              placeholder="Where?"
              className="border border-gray-300 rounded-md py-2 px-3 w-full"
            />
          </div>
          <div className="flex flex-col w-full">
            <label className="text-sm text-gray-600 mb-1">Date</label>
            <input
              type="date"
              className="border border-gray-300 rounded-md py-2 px-3 w-full"
            />
          </div>
          <div className="flex flex-col w-full">
            <label className="text-sm text-gray-600 mb-1">Time</label>
            <select className="border border-gray-300 rounded-md py-2 px-3 w-full">
              <option>When?</option>
            </select>
          </div>
          {/* <div className="flex flex-col w-full">
            <label className="text-sm text-gray-600 mb-1">Duration</label>
            <select className="border border-gray-300 rounded-md py-2 px-3 w-full">
              <option>How long?</option>
            </select>
          </div> */}
          <div className="flex flex-col w-full">
            <label className="invisible mb-1">Search</label>
            <button className="bg-green-600 text-white px-4 py-2 rounded-md w-full">
              Search
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="mt-6 border-t pt-4">
          <p className="text-sm text-gray-500 mb-2">🔎 Quick filters:</p>
          <div className="flex flex-wrap gap-3">
            {[
              "Available Today",
              "Near Me",
              "Highly Rated",
              "Instant Booking",
              "Free Consultation",
            ].map((tag) => (
              <span
                key={tag}
                className="border border-green-400 text-green-600 text-sm px-4 py-1 rounded-full cursor-pointer hover:bg-green-50"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchFilter;
