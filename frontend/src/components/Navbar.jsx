import React from "react";
import { Calendar } from "lucide-react";

const Navbar = () => {
  return (
    <nav className="w-[80vw] px-12 py-6 m-auto flex justify-around items-center ">
      {/* Logo */}
      <div className="flex items-center gap-2">
        <div className="bg-green-600 p-2 rounded-md">
          <Calendar className="text-white w-5 h-5" />
        </div>
        <span className="font-semibold text-lg text-gray-900">BookEase</span>
      </div>

      {/* Menu Items */}
      <ul className="hidden md:flex gap-10 text-sm text-gray-800">
        <li className="hover:text-green-600 cursor-pointer">Services</li>
        <li className="hover:text-green-600 cursor-pointer">Locations</li>
        <li className="hover:text-green-600 cursor-pointer">About</li>
        <li className="hover:text-green-600 cursor-pointer">Contact</li>
        <li className="hover:text-green-600 cursor-pointer">Sign in</li>
      </ul>

      {/* Get Started Button */}
      <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-full text-sm font-medium">
        Get Started
      </button>
    </nav>
  );
};

export default Navbar;
