import React, { useState } from "react";
// import axios from "axios";
import baseURL from '../api/axios.js'
import { useNavigate } from "react-router-dom";


export default function SignupPage() {

    const navigate = useNavigate();
  const [form, setForm] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  });


  const handleChange = (e) =>
    setForm({ ...form, [e.target.name]: e.target.value });
  const handleSubmit = async (e) => {
    e.preventDefault();

    if(form.password != form.confirmPassword){
        alert("Passwords do not match");
        return;
    }

    try {
        const res = await baseURL.post('/user/register',{
            name : form.name,
            email: form.email,
            password:form.password
        },{
            withCredentials:true,
        })

        console.log('User registered',res.data);
        navigate('/login');
        
    } catch (error) {
        console.log('Registration failed',error);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-pink-50 to-white">
      <div className="max-w-md w-full bg-white rounded-xl shadow-md p-8 space-y-8">
        <h2 className="text-center text-3xl font-bold text-gray-900">Create your account</h2>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm -space-y-px">
            <input
              type="text"
              name="name"
              placeholder="Full Name"
              required
              value={form.name}
              onChange={handleChange}
              className="appearance-none rounded-t-md px-3 py-2 border border-gray-300 placeholder-gray-500 w-full focus:outline-none focus:ring-pink-500 focus:border-pink-500"
            />
            <input
              type="email"
              name="email"
              placeholder="Email address"
              required
              value={form.email}
              onChange={handleChange}
              className="appearance-none px-3 py-2 border border-gray-300 placeholder-gray-500 w-full focus:outline-none focus:ring-pink-500 focus:border-pink-500 mt-2"
            />
            <input
              type="password"
              name="password"
              placeholder="Password"
              required
              value={form.password}
              onChange={handleChange}
              className="appearance-none px-3 py-2 border border-gray-300 placeholder-gray-500 w-full focus:outline-none focus:ring-pink-500 focus:border-pink-500 mt-2"
            />
            <input
              type="password"
              name="confirmPassword"
              placeholder="Confirm Password"
              required
              value={form.confirmPassword}
              onChange={handleChange}
              className="appearance-none rounded-b-md px-3 py-2 border border-gray-300 placeholder-gray-500 w-full focus:outline-none focus:ring-pink-500 focus:border-pink-500 mt-2"
            />
          </div>
          <button
            type="submit"
            className="group w-full flex justify-center py-2 px-4 border border-transparent font-medium rounded-md text-white bg-blue-500 hover:bg-pink-600"
          >
            Sign up
          </button>
        </form>
        <p className="text-center text-sm text-gray-500 mt-4">
          Already have an account?{" "}
          <a href="/" className="text-blue-500 hover:underline">Log in</a>
        </p>
      </div>
    </div>
  );
}
