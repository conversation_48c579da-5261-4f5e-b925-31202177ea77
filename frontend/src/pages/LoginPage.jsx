import React, { useState } from "react";
import baseURL from '../api/axios.js'
import { useNavigate } from "react-router-dom";

export default function LoginPage() {

  const navigate = useNavigate();

  const [form, setForm] = useState({ email: "", password: "" });
  const handleChange = (e) =>
    setForm({ ...form, [e.target.name]: e.target.value });
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      const res = await baseURL.post('/user/login',{
        email:form.email,
        password:form.password
      },{
        withCredentials:true,
      })

      console.log(res.data);
      navigate('/')
    } catch (error) {
      console.log('Login Failed',error);
      
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-indigo-50 to-white">
      <div className="max-w-md w-full bg-white rounded-xl shadow-md p-8 space-y-8">
        <h2 className="text-center text-3xl font-bold text-gray-900">Login to your account</h2>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm -space-y-px">
            <input
              type="email"
              name="email"
              autoComplete="email"
              required
              placeholder="Email address"
              value={form.email}
              onChange={handleChange}
              className="appearance-none rounded-t-md px-3 py-2 border border-gray-300 placeholder-gray-500 w-full focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
            <input
              type="password"
              name="password"
              required
              placeholder="Password"
              value={form.password}
              onChange={handleChange}
              className="appearance-none rounded-b-md px-3 py-2 border border-gray-300 placeholder-gray-500 w-full focus:outline-none focus:ring-blue-500 focus:border-blue-500 mt-2"
            />
          </div>
          <div className="flex items-center justify-between">
            <label className="flex items-center text-sm text-gray-600">
              <input type="checkbox" className="form-checkbox" />
              <span className="ml-2">Remember me</span>
            </label>
            <a href="/forgot-password" className="text-sm text-blue-600 hover:underline">
              Forgot password?
            </a>
          </div>
          <button
            type="submit"
            className="group w-full flex justify-center py-2 px-4 border border-transparent font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Login
          </button>
        </form>
        <p className="text-center text-sm text-gray-500 mt-4">
          Don&apos;t have an account?{" "}
          <a href="/signup" className="text-blue-600 hover:underline">Sign up</a>
        </p>
      </div>
    </div>
  );
}
