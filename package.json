{"name": "bookease", "version": "1.0.0", "main": "backend/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "type": "module", "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@tailwindcss/vite": "^4.1.11", "axios": "^1.11.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.18.0", "mongoose": "^8.16.4", "react-router-dom": "^6.30.1", "tailwindcss": "^4.1.11", "uuid": "^11.1.0"}}