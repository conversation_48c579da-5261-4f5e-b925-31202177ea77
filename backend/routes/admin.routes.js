import express from 'express';
import { register,login,approveOwner,getPendingOwners } from '../controllers/admin.controller.js';
import { authorizeRoles } from '../middlewares/authorizeRoles.js';
import authMiddleware from '../middlewares/auth.middleware.js'
import { logout } from '../controllers/user.controller.js';

const adminRouter = express.Router();

adminRouter.post('/register',register);
adminRouter.post('/login',login)
adminRouter.post('/logout',authMiddleware,logout)
adminRouter.patch('/approve-owner/:ownerId',authMiddleware,authorizeRoles('admin'),approveOwner);
adminRouter.get('/get-pending-req',authMiddleware,authorizeRoles('admin'),getPendingOwners);

export default adminRouter;