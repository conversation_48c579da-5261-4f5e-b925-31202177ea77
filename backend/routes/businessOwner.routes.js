import express from "express";
import {
  login,
  register,
  businessRegistration,
  serviceRegistration,
  editBusiness,
  editService,
  logout
} from "../controllers/businessOwner.controller.js";

import authMiddleware from "../middlewares/auth.middleware.js";
import { authorizeRoles } from "../middlewares/authorizeRoles.js";

const ownerRouter = express.Router();

// Public Routes
ownerRouter.post('/register', register);
ownerRouter.post('/login', login);
ownerRouter.post('/logout', authMiddleware, logout);

// Protected Routes (Owner Only)
ownerRouter.post(
  '/create-business',
  authMiddleware,
  authorizeRoles('owner'),
  businessRegistration
);

ownerRouter.patch(
  '/edit-business/:businessId',
  authMiddleware,
  authorizeRoles('owner'),
  editBusiness
);

ownerRouter.post(
  '/create-service',
  authMiddleware,
  authorizeRoles('owner'),
  serviceRegistration
);

ownerRouter.patch(
  '/edit-service/:serviceId',
  authMiddleware,
  authorizeRoles('owner'),
  editService
);

export default ownerRouter;
