import User from "../models/User.js";
// import jwt from "jsonwebtoken";
import bcrypt from "bcrypt";
import dotenv from "dotenv";
import { generateToken } from "../utils/generateToken.js";
import Session from "../models/Session.js";

dotenv.config();
// const SECRET_KEY = process.env.SECRET_KEY;

const register = async (req, res) => {
  try {
    const { name, password, email, phone } = req.body;

    const userExists = await User.findOne({ email });
    if (userExists)
      return res.status(400).json({ message: "Email already registered." });

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    const newUser = await User.create({
      name,
      password: hashedPassword,
      email,
      phone,
    });

    newUser.isLoggedIn = true;
    await newUser.save();

    const token = await generateToken(newUser);

    res.status(201).json({ message: "Registration Successful.", token });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: "Server error" });
  }
};

const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    const userExists = await User.findOne({ email });
    if (!userExists)
      return res.status(401).json({ message: "Email is not registered." });

    const isPasswordCorrect = await bcrypt.compare(
      password,
      userExists.password
    );
    if (!isPasswordCorrect)
      return res.status(401).json({ message: "Invalid password." });

    const token = await generateToken(userExists);
    userExists.isLoggedIn=true;

    await userExists.save();


    res.status(200).json({ message: "Login Successful", token });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: "Server error" });
  }
};

// export const logoutOneDevice = async (req, res) => {
//   try {
//     const { id: userId, sessionId } = req.user;
//     await Session.deleteOne({ userId, sessionId });
//     return res.status(200).json({ message: 'Logged out from current device' });
//   } catch (error) {
//     console.error('Logout error:', error);
//     return res.status(500).json({ message: 'Logout failed' });
//   }
// };

const logout = async (req, res) => {
  try {
    const { id: userId } = req.user;
    await Session.deleteMany({ userId });
    return res.status(200).json({ message: 'Logged out from all devices' });
  } catch (error) {
    console.error('Logout all devices error:', error);
    return res.status(500).json({ message: 'Logout from all devices failed' });
  }
};


export { register, login ,logout };
