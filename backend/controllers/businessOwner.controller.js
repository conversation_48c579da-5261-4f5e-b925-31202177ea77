import Owner from "../models/BusinessOwner.js";
import Business from "../models/Business.js";
import Service from "../models/Service.js";
// import jwt from "jsonwebtoken";
import { generateToken } from "../utils/generateToken.js";
import bcrypt from "bcrypt";
import Session from "../models/Session.js";

// 1. REGISTER OWNER
const register = async (req, res) => {
  try {
    const { name, email, password, phone } = req.body;

    const userExists = await Owner.findOne({ email });
    if (userExists)
      return res.status(400).json({ message: "Email already registered" });

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    const newUser = await Owner.create({
      name,
      email,
      phone,
      passwordHash: hashedPassword,
      isVerified: false, // ❗Pending approval
    });

    res.status(200).json({ message: "Submitted for admin approval" });
  } catch (error) {
    res.status(500).json({ message: "Server Error" });
  }
};

// 2. OWNER LOGIN
const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    const owner = await Owner.findOne({ email });
    if (!owner){
      return res.status(400).json({ message: "Email not registered" });
    }

    if (!owner.isVerified)
      return res.status(403).json({ message: "Account pending admin approval" });

    const isPasswordCorrect = await bcrypt.compare(password, owner.passwordHash);
    if (!isPasswordCorrect)
      return res.status(401).json({ message: "Invalid password" });

    const token = await generateToken(owner);

    res.status(200).json({ message: "Login successful", token });
  } catch (error) {
    res.status(500).json({ message: "Server error" });
  }
};

// 3. BUSINESS REGISTRATION
const businessRegistration = async (req, res) => {
  try {
    const { name, type, description, location, contact, workingHours } =
      req.body;

    const ownerId = req.user.user_id;

    const newBusiness = await Business.create({
      owner: ownerId,
      name,
      type,
      description,
      location,
      contact,
      workingHours,
    });

    res.status(201).json({ message: "Business created successfully", newBusiness });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server Error" });
  }
};

// 4. EDIT BUSINESS
const editBusiness = async (req, res) => {
  const { businessId } = req.params;
  const { name, description, type } = req.body;

  try {
    const business = await Business.findById(businessId);
    if (!business) return res.status(404).json({ message: "Business not found" });

    business.name = name || business.name;
    business.description = description || business.description;
    business.type = type || business.type;

    await business.save();
    res.status(200).json({ message: "Edited Successfully", business });
  } catch (error) {
    res.status(500).json({ message: "Server Error" });
  }
};

// 5. ADD SERVICE
const serviceRegistration = async (req, res) => {
  const { name, description, duration, price, category, image, isActive } =
    req.body;

  const ownerId = req.user.user_id;
  try {
    const newService = await Service.create({
      owner: ownerId,
      name,
      description,
      duration,
      price,
      category,
      image,
      isActive,
    });
    res.status(201).json({ message: "Service added", newService });
  } catch (error) {
    res.status(500).json({ message: "Server error" });
  }
};

// 6. EDIT SERVICE
const editService = async (req, res) => {
  const { serviceId } = req.params;
  const { name, description, duration, price, category, image, isActive } = req.body;

  try {
    const service = await Service.findById(serviceId);
    if (!service) return res.status(404).json({ message: "Service not found" });

    service.name = name || service.name;
    service.description = description || service.description;
    service.duration = duration || service.duration;
    service.price = price || service.price;
    service.category = category || service.category;
    service.image = image || service.image;
    service.isActive = isActive !== undefined ? isActive : service.isActive;

    await service.save(); // 🛠️ Fixed this line
    res.status(200).json({ message: "Service updated", service });
  } catch (error) {
    res.status(500).json({ message: "Server error" });
  }
};


// const logoutbyOneDevice = async (req, res) => {
//   try {
//     const { id: userId, sessionId } = req.user;
//     await Session.deleteOne({ userId, sessionId });
//     return res.status(200).json({ message: 'Owner logged out from current device' });
//   } catch (error) {
//     console.error('Owner logout error:', error);
//     return res.status(500).json({ message: 'Logout failed' });
//   }
// };

const logout = async (req, res) => {
  try {
    if (!req.user)
      return res.status(401).json({ message: "User not logged in" });

    const { id: userId } = req.user;
    await Session.deleteMany({ userId });

    return res.status(200).json({ message: 'Owner logged out from all devices' });
  } catch (error) {
    console.error('Owner logoutAll error:', error);
    return res.status(500).json({ message: 'Logout all failed' });
  }
};



export {
  register,
  login,
  businessRegistration,
  editBusiness,
  serviceRegistration,
  editService,
  logout
};
