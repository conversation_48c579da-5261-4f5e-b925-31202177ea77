import Admin from "../models/Admin.js";
import Owner from "../models/BusinessOwner.js";
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { generateToken } from "../utils/generateToken.js";
import Session from "../models/Session.js";

// this is for creating the first admin only.

const register = async (req,res) =>{
    try {
    const {name, password, email} = req.body;
    const userExists = await Admin.findOne({email});
    if(userExists) return res.status(400).json({message:"Admin already exixts"});

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password,salt);

    const newUser = await Admin.create({
        name,
        email,
        password:hashedPassword
    });

    const token = generateToken(newUser)


    res.status(200).json({message:"User registered",token});
    } catch (error) {
        res.status(500).json({message:"Server Error"}); 
    }
}


const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    const admin = await Admin.findOne({ email });
    if (!admin)
      return res.status(400).json({ message: "Email not registered" });

    const isPasswordCorrect = await bcrypt.compare(password, admin.password);
    if (!isPasswordCorrect)
      return res.status(400).json({ message: "Incorrect password" });

    const token = await generateToken(admin); // uses session-based version

    res.status(200).json({ message: "Login successful", token });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({ message: "Server error" });
  }
};

// controllers/adminController.js
const approveOwner = async (req, res) => {
  const { ownerId } = req.params;

  try {
    const owner = await Owner.findById(ownerId);
    if (!owner) return res.status(404).json({ message: "Owner not found" });

    owner.isVerified = true;
    await owner.save();

    res.status(200).json({ message: "Owner approved successfully" });
  } catch (err) {
    res.status(500).json({ message: "Server error" });
  }
};


const getPendingOwners = async (req, res) => {
  const pendingOwners = await Owner.find({ isVerified: false });
  res.status(200).json(pendingOwners);
};

const logout = async(req,res)=>{
  try {
    const { id: userId } = req.user;
    await Session.deleteMany({ userId });
    return res.status(200).json({ message: 'Admin logged out from all devices' });
  } catch (error) {
    console.error('Admin logoutAll error:', error);
    return res.status(500).json({ message: 'Logout all failed' });
  }
}




export {register,login,approveOwner,getPendingOwners,logout}