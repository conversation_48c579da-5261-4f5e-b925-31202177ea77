// import mongoose from "mongoose";

// const connectDB = new mongoose.

// iSybSw1aM6MWjLa7
// mongodb+srv://coc88401:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0

import mongoose from "mongoose";

const connectDB = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log("MongoDB connected");
    } catch (error) {
        console.log(error);
    }
}

export default connectDB;