import express from "express";
import dotenv from "dotenv";
import connectDB from "./config/db.js";
import cors from "cors";
import userRouter from "./routes/user.routes.js";
import ownerRouter from "./routes/businessOwner.routes.js"
import adminRouter from "./routes/admin.routes.js";

dotenv.config();
const app = express();


// app.use(cors());
app.use(express.json());

const corsOptions = {
  origin:"http://localhost:5173",
  credentials: true,
};
app.use(cors(corsOptions));

app.use('/api/user',userRouter);
app.use('/api/owner',ownerRouter);
app.use('/api/admin',adminRouter);



const PORT = process.env.PORT;

app.listen(PORT, () => {
  connectDB();
  console.log("server is running at port 5000");
});
