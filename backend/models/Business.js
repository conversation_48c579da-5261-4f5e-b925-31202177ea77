// models/Shop.js
import mongoose from "mongoose";

const businessSchema = new mongoose.Schema({
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "BusinessOwner",
    required: true
  },
  name: { type: String, required: true },
  type: { type: String, required: true }, // salon, clinic, etc.
  description: String,
  location: {
    type:Array,
    address: String,
    city: String,
    state: String,
    pincode: String,
    coordinates: {
      lat: Number,
      lng: Number
    }
  },
  contact: {
    phone: String,
    email: String,
    whatsapp: String
  },
  workingHours: {
    open: String,
    close: String,
    closedDays: [String] // e.g., ["Sunday"]
  },
  image: String, // logo or banner URL
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now }
});

export default mongoose.model("Shop", businessSchema);
