import mongoose from "mongoose";

const BusinessOwnerSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      required: true,
    },
    phone: {
      type: Number,
      required: true,
    },
    password: {
      type: String,
      required: true,
    },
    type:{
        type:String,
        default:''
    },
    role: {
      type: String,
      default: 'owner',
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

const Owner = mongoose.model('Owner',BusinessOwnerSchema);

export default Owner;