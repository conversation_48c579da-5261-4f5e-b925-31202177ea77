// models/Service.js
import mongoose from "mongoose";

const serviceSchema = new mongoose.Schema({
  shop: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Shop",
    required: true,
  },
  name: { type: String, required: true },
  description: String,
  duration: { type: Number, required: true }, // in minutes
  price: { type: Number, required: true },
  category: String, // optional tag like "Hair", "Consultation"
  image: String,
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
});

export default mongoose.model("Service", serviceSchema);
