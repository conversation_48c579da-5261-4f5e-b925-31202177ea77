import mongoose from "mongoose";

const userSchema = new mongoose.Schema(
  {
    name: { 
        type: String, 
        required: true 
    },
    email: { 
        type: String,
         unique: true, 
         required: true 
        },
    phone: { 
        type: String,
        unique: true 
        },
    password: { 
        type: String,
        required: true 
        },
    profileImage: { 
        type: String 
    },
    bookings: [{ 
        type: mongoose.Schema.Types.ObjectId, 
        ref: "Booking" 
    }],
    favourites: [{ 
        type: mongoose.Schema.Types.ObjectId, 
        ref: "Shop" 
    }],
    role: { 
        type: String, 
        default: "user" 
    },
    isLoggedIn: { 
        type: Boolean, 
        default: false 
    },
  },
  { timestamps: true }
);

const User = mongoose.model('User', userSchema);

export default User;